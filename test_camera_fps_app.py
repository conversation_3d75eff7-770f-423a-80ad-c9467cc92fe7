#!/usr/bin/env python3
"""
Test Camera FPS Monitor Application
测试摄像头FPS监控应用

Quick test to verify the camera FPS monitor application works correctly.
"""

import sys
import subprocess
import time
import threading
import signal


def test_imports():
    """Test if all required modules can be imported."""
    print("🔍 Testing imports...")
    
    try:
        import cv2
        print(f"  ✅ OpenCV: {cv2.__version__}")
    except ImportError as e:
        print(f"  ❌ OpenCV: {e}")
        return False
    
    try:
        import numpy as np
        print(f"  ✅ NumPy: {np.__version__}")
    except ImportError as e:
        print(f"  ❌ NumPy: {e}")
        return False
    
    try:
        import streamlit as st
        print(f"  ✅ Streamlit: {st.__version__}")
    except ImportError as e:
        print(f"  ❌ Streamlit: {e}")
        return False
    
    return True


def test_camera_access():
    """Test basic camera access."""
    print("\n📹 Testing camera access...")
    
    import cv2
    
    cameras_tested = [0, 1, 2, 3, 4, 5, 6, 7]
    working_cameras = []
    
    for cam_idx in cameras_tested:
        try:
            cap = cv2.VideoCapture(cam_idx)
            if cap.isOpened():
                ret, frame = cap.read()
                if ret and frame is not None:
                    height, width = frame.shape[:2]
                    working_cameras.append(cam_idx)
                    print(f"  ✅ Camera {cam_idx}: {width}x{height}")
                else:
                    print(f"  ❌ Camera {cam_idx}: Cannot read frames")
                cap.release()
            else:
                print(f"  ❌ Camera {cam_idx}: Cannot open")
        except Exception as e:
            print(f"  ❌ Camera {cam_idx}: Error - {e}")
        
        time.sleep(0.1)
    
    print(f"\n📊 Found {len(working_cameras)} working cameras: {working_cameras}")
    return working_cameras


def test_app_syntax():
    """Test if the application file has correct syntax."""
    print("\n🔍 Testing application syntax...")
    
    try:
        result = subprocess.run([
            sys.executable, "-m", "py_compile", "streamlit_camera_fps_only.py"
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            print("  ✅ Application syntax is correct")
            return True
        else:
            print(f"  ❌ Syntax error: {result.stderr}")
            return False
    except subprocess.TimeoutExpired:
        print("  ⚠️  Syntax check timed out")
        return False
    except Exception as e:
        print(f"  ❌ Error checking syntax: {e}")
        return False


def test_streamlit_run():
    """Test if Streamlit can start the application."""
    print("\n🌐 Testing Streamlit application startup...")
    print("  This will start the app for 10 seconds to verify it works...")
    
    try:
        # Start Streamlit in background
        process = subprocess.Popen([
            sys.executable, "-m", "streamlit", "run", 
            "streamlit_camera_fps_only.py",
            "--server.headless", "true",
            "--server.port", "8502",  # Use different port to avoid conflicts
            "--browser.gatherUsageStats", "false"
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
        
        # Wait a bit for startup
        time.sleep(5)
        
        # Check if process is still running
        if process.poll() is None:
            print("  ✅ Streamlit application started successfully")
            print("  📝 App would be available at: http://localhost:8502")
            
            # Terminate the process
            process.terminate()
            try:
                process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                process.kill()
            
            return True
        else:
            stdout, stderr = process.communicate()
            print(f"  ❌ Application failed to start")
            print(f"  📝 Error: {stderr}")
            return False
            
    except Exception as e:
        print(f"  ❌ Error testing Streamlit: {e}")
        return False


def run_quick_demo():
    """Run a quick demo of the camera functionality."""
    print("\n🎬 Running quick camera demo...")
    print("  This will capture a few frames from available cameras...")
    
    import cv2
    import numpy as np
    
    # Test cameras
    working_cameras = []
    for cam_idx in [0, 2, 4, 6]:
        try:
            cap = cv2.VideoCapture(cam_idx)
            if cap.isOpened():
                ret, frame = cap.read()
                if ret and frame is not None:
                    working_cameras.append((cam_idx, cap))
                    print(f"  📹 Camera {cam_idx}: Ready")
                else:
                    cap.release()
            else:
                print(f"  ❌ Camera {cam_idx}: Not available")
        except Exception as e:
            print(f"  ❌ Camera {cam_idx}: Error - {e}")
    
    if not working_cameras:
        print("  ❌ No working cameras found for demo")
        return False
    
    print(f"  🎯 Testing {len(working_cameras)} cameras for 5 seconds...")
    
    # Capture frames for 5 seconds
    start_time = time.time()
    frame_counts = {cam_idx: 0 for cam_idx, _ in working_cameras}
    
    try:
        while time.time() - start_time < 5:
            for cam_idx, cap in working_cameras:
                ret, frame = cap.read()
                if ret:
                    frame_counts[cam_idx] += 1
            time.sleep(0.03)  # ~30 FPS
    
    except KeyboardInterrupt:
        print("  🛑 Demo interrupted")
    
    finally:
        # Cleanup
        for cam_idx, cap in working_cameras:
            cap.release()
    
    # Calculate and display FPS
    elapsed = time.time() - start_time
    print(f"\n  📊 Demo Results ({elapsed:.1f}s):")
    for cam_idx in frame_counts:
        fps = frame_counts[cam_idx] / elapsed
        status = "🟢" if fps > 15 else "🟡" if fps > 5 else "🔴"
        print(f"    {status} Camera {cam_idx}: {fps:.1f} FPS ({frame_counts[cam_idx]} frames)")
    
    return True


def main():
    """Main test function."""
    print("🧪 Camera FPS Monitor Application Test")
    print("=" * 50)
    
    tests = [
        ("Import Test", test_imports),
        ("Camera Access Test", test_camera_access),
        ("Application Syntax Test", test_app_syntax),
        ("Streamlit Startup Test", test_streamlit_run),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n🔬 Running {test_name}...")
        try:
            result = test_func()
            results[test_name] = result
            if result:
                print(f"  ✅ {test_name}: PASSED")
            else:
                print(f"  ❌ {test_name}: FAILED")
        except Exception as e:
            print(f"  ❌ {test_name}: ERROR - {e}")
            results[test_name] = False
    
    # Summary
    print(f"\n📋 Test Summary")
    print("=" * 30)
    passed = sum(results.values())
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"  {status}: {test_name}")
    
    print(f"\n🎯 Overall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The application should work correctly.")
        
        # Offer to run demo
        try:
            response = input("\n🎬 Run quick camera demo? (y/n): ").strip().lower()
            if response in ['y', 'yes']:
                run_quick_demo()
        except KeyboardInterrupt:
            print("\n👋 Test completed")
    else:
        print("⚠️  Some tests failed. Please check the errors above.")
        print("\n💡 Common solutions:")
        print("  • Install missing packages: pip3 install opencv-python numpy streamlit")
        print("  • Check camera connections")
        print("  • Close other camera applications")
    
    print(f"\n📝 Next steps:")
    print(f"  • Run the application: python3 -m streamlit run streamlit_camera_fps_only.py")
    print(f"  • Or use the launcher: ./run_camera_fps_monitor.sh")


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n🛑 Test interrupted by user")
        sys.exit(0)
