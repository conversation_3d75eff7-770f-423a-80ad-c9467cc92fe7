#!/usr/bin/env python3
"""
Streamlit Multi-Camera FPS Monitor (No YOLO Detection)
Streamlit多摄像头FPS监控器（无YOLO检测）

A pure camera FPS monitoring application without any AI detection.
Focus on displaying camera feeds and monitoring FPS performance.
"""

import io
import threading
import time
from typing import Any, Dict, List, Tuple

import cv2
import numpy as np

from ultralytics.utils import LOGGER
from ultralytics.utils.checks import check_requirements

# Check Streamlit requirements
check_requirements("streamlit>=1.29.0")
import streamlit as st


class CameraFPSMonitor:
    """
    A class to monitor multiple cameras and display FPS without any AI detection.
    
    This class provides functionalities for configuring camera settings, monitoring FPS,
    and displaying real-time camera feeds using Streamlit interface.
    
    Attributes:
        st (module): Streamlit module for UI creation.
        num_cameras (int): Number of cameras to use.
        camera_indices (List[int]): List of camera indices to use.
        frame_width (int): Custom frame width for cameras.
        frame_height (int): Custom frame height for cameras.
        camera_caps (List[cv2.VideoCapture]): List of camera capture objects.
        camera_threads (List[threading.Thread]): List of camera threads.
        camera_frames (Dict[int, np.ndarray]): Dictionary to store latest frames from each camera.
        camera_running (bool): Flag to control camera threads.
        camera_containers (Dict[int, Tuple[Any, Any]]): UI containers for each camera.
        camera_fps (Dict[int, float]): FPS for each camera.
        camera_frame_times (Dict[int, List[float]]): Frame timestamps for FPS calculation.
        fps_containers (Dict[int, Any]): UI containers for FPS display.
        overall_fps_container: Container for overall FPS display.
    """

    def __init__(self) -> None:
        """Initialize the CameraFPSMonitor class."""
        self.st = st
        
        # Camera configuration
        self.num_cameras = 4  # Default to 4 cameras
        self.camera_indices = [0, 2, 4, 6]  # Default camera indices
        self.frame_width = 640  # Default frame width
        self.frame_height = 360  # Default frame height
        
        # Camera objects and threads
        self.camera_caps: List[cv2.VideoCapture] = []
        self.camera_threads: List[threading.Thread] = []
        self.camera_frames: Dict[int, np.ndarray] = {}
        self.camera_running = False
        self.camera_containers: Dict[int, Tuple[Any, Any]] = {}
        
        # FPS monitoring
        self.camera_fps: Dict[int, float] = {}
        self.camera_frame_times: Dict[int, List[float]] = {}
        self.fps_containers: Dict[int, Any] = {}
        self.overall_fps_container = None
        
        LOGGER.info("CameraFPSMonitor initialized")

    def web_ui(self) -> None:
        """Set up the Streamlit web interface."""
        # Hide main menu
        menu_style_cfg = """<style>MainMenu {visibility: hidden;}</style>"""
        
        # Main title
        main_title_cfg = """<div><h1 style="color:#111F68; text-align:center; font-size:40px; margin-top:-50px;
        font-family: 'Archivo', sans-serif; margin-bottom:20px;">Multi-Camera FPS Monitor</h1></div>"""
        
        # Subtitle
        sub_title_cfg = """<div><h5 style="color:#042AFF; text-align:center; font-family: 'Archivo', sans-serif; 
        margin-top:-15px; margin-bottom:50px;">Real-time camera monitoring without AI detection - 
        Pure FPS performance analysis! 📹</h5></div>"""
        
        # Set page configuration
        self.st.set_page_config(page_title="Camera FPS Monitor", layout="wide")
        self.st.markdown(menu_style_cfg, unsafe_allow_html=True)
        self.st.markdown(main_title_cfg, unsafe_allow_html=True)
        self.st.markdown(sub_title_cfg, unsafe_allow_html=True)

    def sidebar(self) -> None:
        """Configure the Streamlit sidebar for camera settings."""
        self.st.sidebar.title("Camera Configuration")
        
        # Camera count selection
        self.num_cameras = self.st.sidebar.slider("Number of Cameras", 1, 8, self.num_cameras)
        
        # Camera indices selection with presets
        self.st.sidebar.subheader("Camera Selection")
        self.st.sidebar.info("💡 **Tip**: Cameras 0,2 usually work well together. Avoid using adjacent indices simultaneously.")
        
        preset_configs = {
            "Reliable (0,2)": "0,2",
            "All Available (0,2,4,6)": "0,2,4,6", 
            "Sequential (0,1,2,3)": "0,1,2,3",
            "Even Numbers (0,2,4,6)": "0,2,4,6",
            "Odd Numbers (1,3,5,7)": "1,3,5,7",
            "Custom": "custom"
        }
        
        selected_preset = self.st.sidebar.selectbox("Camera Configuration", list(preset_configs.keys()))
        
        if selected_preset == "Custom":
            camera_indices_str = self.st.sidebar.text_input(
                "Camera Indices (comma-separated)",
                ",".join(map(str, self.camera_indices[:self.num_cameras]))
            )
        else:
            camera_indices_str = preset_configs[selected_preset]
        
        try:
            self.camera_indices = [int(x.strip()) for x in camera_indices_str.split(",")][:self.num_cameras]
            self.num_cameras = len(self.camera_indices)
        except ValueError:
            self.st.sidebar.error("Invalid camera indices format. Using default values.")
            self.camera_indices = [0, 2, 4, 6]
            self.num_cameras = 4
        
        # Resolution settings
        self.st.sidebar.subheader("Resolution Settings")
        resolution_presets = {
            "640x360": (640, 360),
            "640x480": (640, 480),
            "800x600": (800, 600),
            "1280x720": (1280, 720),
            "1920x1080": (1920, 1080),
            "Custom": None
        }
        
        current_resolution = f"{self.frame_width}x{self.frame_height}"
        if current_resolution not in resolution_presets:
            current_resolution = "Custom"
        
        selected_resolution = self.st.sidebar.selectbox(
            "Resolution",
            list(resolution_presets.keys()),
            index=list(resolution_presets.keys()).index(current_resolution) if current_resolution in resolution_presets else 0
        )
        
        if selected_resolution == "Custom":
            new_width = self.st.sidebar.number_input("Width", min_value=320, max_value=3840, value=self.frame_width)
            new_height = self.st.sidebar.number_input("Height", min_value=240, max_value=2160, value=self.frame_height)
            self.frame_width = int(new_width)
            self.frame_height = int(new_height)
        else:
            self.frame_width, self.frame_height = resolution_presets[selected_resolution]
        
        # Display current settings
        self.st.sidebar.info(f"Current resolution: {self.frame_width}x{self.frame_height}")
        self.st.sidebar.info(f"Selected cameras: {self.camera_indices}")
        
        # Performance settings
        self.st.sidebar.subheader("Performance Settings")
        self.show_fps_overlay = self.st.sidebar.checkbox("Show FPS Overlay on Frames", value=True)
        self.fps_update_interval = self.st.sidebar.slider("FPS Update Interval (seconds)", 0.5, 5.0, 1.0, 0.5)

        # Display settings
        self.st.sidebar.subheader("Display Settings")
        self.frame_quality = self.st.sidebar.slider("Frame Quality (%)", 50, 100, 85, 5)
        self.show_timestamps = self.st.sidebar.checkbox("Show Timestamps", value=False)
        
        # Create UI layout for cameras
        self.create_camera_layout()

    def create_camera_layout(self) -> None:
        """Create the camera display layout."""
        # Overall FPS display
        self.overall_fps_container = self.st.empty()
        
        # Create grid layout for cameras
        cols_per_row = 2 if self.num_cameras <= 4 else 3
        rows = (self.num_cameras + cols_per_row - 1) // cols_per_row
        
        for row in range(rows):
            cols = self.st.columns(cols_per_row)
            for col_idx in range(cols_per_row):
                cam_idx = row * cols_per_row + col_idx
                if cam_idx < self.num_cameras:
                    with cols[col_idx]:
                        self.st.markdown(f"**Camera {self.camera_indices[cam_idx]}**")
                        fps_container = self.st.empty()
                        frame_container = self.st.empty()
                        self.camera_containers[cam_idx] = frame_container
                        self.fps_containers[cam_idx] = fps_container

    def setup_cameras(self) -> bool:
        """Initialize multiple cameras with custom resolution."""
        self.st.info(f"🎥 Initializing {len(self.camera_indices)} cameras: {self.camera_indices}")
        
        self.camera_caps = []
        self.camera_frames = {}
        self.camera_fps = {}
        self.camera_frame_times = {}
        successful_cameras = 0
        
        for i, cam_idx in enumerate(self.camera_indices):
            try:
                cap = cv2.VideoCapture(cam_idx)
                
                if cap.isOpened():
                    # Set camera properties
                    cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)
                    cap.set(cv2.CAP_PROP_FOURCC, cv2.VideoWriter_fourcc(*'MJPG'))
                    cap.set(cv2.CAP_PROP_FRAME_WIDTH, self.frame_width)
                    cap.set(cv2.CAP_PROP_FRAME_HEIGHT, self.frame_height)
                    
                    time.sleep(0.1)  # Give camera time to adjust
                    
                    # Test frame capture
                    test_success = False
                    for attempt in range(3):
                        ret, test_frame = cap.read()
                        if ret and test_frame is not None and test_frame.size > 0:
                            test_success = True
                            break
                        time.sleep(0.05)
                    
                    if test_success:
                        actual_width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
                        actual_height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
                        
                        self.camera_caps.append(cap)
                        self.camera_frames[i] = np.zeros((actual_height, actual_width, 3), dtype=np.uint8)
                        self.camera_fps[i] = 0.0
                        self.camera_frame_times[i] = []
                        successful_cameras += 1
                        
                        LOGGER.info(f"Camera {cam_idx} initialized: {actual_width}x{actual_height}")
                    else:
                        self.camera_caps.append(None)
                        cap.release()
                        LOGGER.warning(f"Camera {cam_idx} opened but cannot read frames")
                else:
                    self.camera_caps.append(None)
                    LOGGER.warning(f"Failed to open camera {cam_idx}")
                
                time.sleep(0.2)  # Delay between camera initializations
                
            except Exception as e:
                self.camera_caps.append(None)
                LOGGER.error(f"Error initializing camera {cam_idx}: {e}")
        
        if successful_cameras > 0:
            if successful_cameras < len(self.camera_indices):
                self.st.warning(f"⚠️ Only {successful_cameras}/{len(self.camera_indices)} cameras initialized successfully.")
            else:
                self.st.success(f"✅ All {successful_cameras} cameras initialized successfully!")
            return True
        else:
            self.st.error("❌ Failed to initialize any cameras.")
            return False

    def camera_thread(self, camera_index: int, cap: cv2.VideoCapture) -> None:
        """Thread function to capture frames from a single camera."""
        consecutive_failures = 0
        max_failures = 10
        
        while self.camera_running and cap is not None and cap.isOpened():
            try:
                ret, frame = cap.read()
                if ret and frame is not None and frame.size > 0:
                    self.camera_frames[camera_index] = frame.copy()
                    
                    # Update FPS calculation
                    current_time = time.time()
                    if camera_index in self.camera_frame_times:
                        self.camera_frame_times[camera_index].append(current_time)
                        # Keep only last 30 frame times for rolling average
                        if len(self.camera_frame_times[camera_index]) > 30:
                            self.camera_frame_times[camera_index].pop(0)
                        
                        # Calculate FPS
                        if len(self.camera_frame_times[camera_index]) > 1:
                            time_diff = (self.camera_frame_times[camera_index][-1] - 
                                       self.camera_frame_times[camera_index][0])
                            if time_diff > 0:
                                self.camera_fps[camera_index] = (len(self.camera_frame_times[camera_index]) - 1) / time_diff
                    
                    consecutive_failures = 0
                else:
                    consecutive_failures += 1
                    if consecutive_failures >= max_failures:
                        LOGGER.error(f"Camera {camera_index} failed {max_failures} consecutive times")
                        break
                
                time.sleep(0.03)  # ~30 FPS max
                
            except Exception as e:
                consecutive_failures += 1
                LOGGER.error(f"Error in camera thread {camera_index}: {e}")
                if consecutive_failures >= max_failures:
                    break
                time.sleep(0.1)

    def start_monitoring(self) -> None:
        """Start camera monitoring."""
        if not self.setup_cameras():
            return
        
        # Start camera threads
        self.camera_running = True
        self.camera_threads = []
        
        for i, cap in enumerate(self.camera_caps):
            if cap is not None:
                thread = threading.Thread(target=self.camera_thread, args=(i, cap))
                thread.daemon = True
                thread.start()
                self.camera_threads.append(thread)
        
        stop_button = self.st.sidebar.button("Stop Monitoring")
        
        try:
            monitoring_start_time = time.time()
            total_frames_processed = 0
            last_fps_update = time.time()
            
            while self.camera_running:
                if stop_button:
                    break
                
                current_time = time.time()
                
                # Process frames from all cameras
                for cam_idx in range(len(self.camera_caps)):
                    if cam_idx in self.camera_frames and cam_idx in self.camera_containers:
                        frame = self.camera_frames[cam_idx].copy()
                        
                        if frame is not None and frame.size > 0:
                            # Add overlays if enabled
                            if self.show_fps_overlay and cam_idx in self.camera_fps:
                                fps_value = self.camera_fps[cam_idx]
                                # FPS display with color coding
                                fps_color = (0, 255, 0) if fps_value > 20 else (0, 255, 255) if fps_value > 10 else (0, 0, 255)
                                cv2.putText(frame, f"FPS: {fps_value:.1f}",
                                          (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, fps_color, 2)
                                cv2.putText(frame, f"Cam {self.camera_indices[cam_idx]}",
                                          (10, 60), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)

                                # Add timestamp if enabled
                                if self.show_timestamps:
                                    timestamp = time.strftime("%H:%M:%S", time.localtime())
                                    cv2.putText(frame, timestamp,
                                              (10, frame.shape[0] - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
                            
                            # Display frame
                            frame_container = self.camera_containers[cam_idx]
                            frame_container.image(frame, channels="BGR", 
                                                caption=f"Camera {self.camera_indices[cam_idx]}")
                            
                            total_frames_processed += 1
                
                # Update FPS displays periodically
                if current_time - last_fps_update >= self.fps_update_interval:
                    # Update individual camera FPS displays
                    for cam_idx in range(len(self.camera_caps)):
                        if cam_idx in self.fps_containers and cam_idx in self.camera_fps:
                            fps_value = self.camera_fps[cam_idx]
                            status_color = "🟢" if fps_value > 20 else "🟡" if fps_value > 10 else "🔴"
                            self.fps_containers[cam_idx].metric(
                                f"{status_color} Camera {self.camera_indices[cam_idx]} FPS",
                                f"{fps_value:.1f}",
                                delta=None
                            )
                    
                    # Update overall statistics
                    if self.overall_fps_container:
                        elapsed_time = current_time - monitoring_start_time
                        overall_fps = total_frames_processed / elapsed_time if elapsed_time > 0 else 0
                        avg_camera_fps = sum(self.camera_fps.values()) / len(self.camera_fps) if self.camera_fps else 0
                        active_cameras = len([cap for cap in self.camera_caps if cap is not None])
                        
                        self.overall_fps_container.markdown(f"""
                        ### 📊 Real-time Performance Metrics
                        - **Overall Processing FPS**: {overall_fps:.1f}
                        - **Average Camera FPS**: {avg_camera_fps:.1f}
                        - **Active Cameras**: {active_cameras}/{len(self.camera_indices)}
                        - **Total Frames Processed**: {total_frames_processed:,}
                        - **Monitoring Time**: {elapsed_time:.1f}s
                        - **Resolution**: {self.frame_width}x{self.frame_height}
                        """)
                    
                    last_fps_update = current_time
                
                time.sleep(0.03)  # ~30 FPS display update
        
        finally:
            self.cleanup()

    def cleanup(self) -> None:
        """Clean up resources."""
        self.camera_running = False
        
        # Wait for threads to finish
        for thread in self.camera_threads:
            if thread.is_alive():
                thread.join(timeout=1.0)
        
        # Release cameras
        for cap in self.camera_caps:
            if cap is not None:
                cap.release()
        
        cv2.destroyAllWindows()

    def run(self) -> None:
        """Run the camera FPS monitoring application."""
        self.web_ui()
        self.sidebar()
        
        if self.st.sidebar.button("Start Monitoring"):
            self.start_monitoring()


def main():
    """Main function to run the application."""
    monitor = CameraFPSMonitor()
    monitor.run()


if __name__ == "__main__":
    main()
