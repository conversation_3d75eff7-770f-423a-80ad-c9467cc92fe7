#!/usr/bin/env python3
"""
Optimized Multi-Camera FPS Monitor
优化的多摄像头FPS监控器

High-performance camera monitoring with aggressive optimizations for maximum FPS.
"""

import threading
import time
from typing import Dict, List, Tuple, Any
import cv2
import numpy as np
from collections import deque

from ultralytics.utils import LOGGER
from ultralytics.utils.checks import check_requirements

check_requirements("streamlit>=1.29.0")
import streamlit as st


class OptimizedCameraFPSMonitor:
    """Optimized camera FPS monitor with aggressive performance tuning."""

    def __init__(self) -> None:
        """Initialize the optimized monitor."""
        self.st = st
        
        # Camera configuration
        self.num_cameras = 4
        self.camera_indices = [0, 2, 4, 6]
        self.frame_width = 640
        self.frame_height = 360
        
        # Performance settings
        self.target_fps = 30
        self.frame_skip = 1  # Skip frames for display
        self.buffer_size = 1  # Minimal buffer
        self.thread_delay = 0.01  # Reduced delay
        
        # Camera objects
        self.camera_caps: List[cv2.VideoCapture] = []
        self.camera_threads: List[threading.Thread] = []
        self.camera_frames: Dict[int, np.ndarray] = {}
        self.camera_running = False
        self.camera_containers: Dict[int, Any] = {}
        
        # FPS monitoring with deque for better performance
        self.camera_fps: Dict[int, float] = {}
        self.camera_frame_times: Dict[int, deque] = {}
        self.fps_containers: Dict[int, Any] = {}
        self.overall_fps_container = None
        
        # Frame counters
        self.camera_frame_counts: Dict[int, int] = {}
        self.camera_start_times: Dict[int, float] = {}

    def web_ui(self) -> None:
        """Set up optimized web interface."""
        self.st.set_page_config(
            page_title="Optimized Camera FPS Monitor", 
            layout="wide",
            initial_sidebar_state="expanded"
        )
        
        self.st.markdown("""
        <style>
        .main > div {padding-top: 2rem;}
        .stMetric {background-color: #f0f2f6; padding: 0.5rem; border-radius: 0.5rem;}
        </style>
        """, unsafe_allow_html=True)
        
        self.st.title("🚀 Optimized Multi-Camera FPS Monitor")
        self.st.markdown("**High-performance camera monitoring with aggressive optimizations**")

    def sidebar(self) -> None:
        """Configure optimized settings."""
        self.st.sidebar.title("⚡ Performance Settings")
        
        # Camera selection
        self.st.sidebar.subheader("Camera Configuration")
        self.num_cameras = self.st.sidebar.slider("Number of Cameras", 1, 8, self.num_cameras)
        
        # Quick presets
        preset_configs = {
            "High Performance (0,2)": "0,2",
            "Quad Cameras (0,2,4,6)": "0,2,4,6",
            "Sequential (0,1,2,3)": "0,1,2,3",
            "Custom": "custom"
        }
        
        selected_preset = self.st.sidebar.selectbox("Camera Preset", list(preset_configs.keys()))
        
        if selected_preset == "Custom":
            camera_indices_str = self.st.sidebar.text_input(
                "Camera Indices", ",".join(map(str, self.camera_indices[:self.num_cameras]))
            )
        else:
            camera_indices_str = preset_configs[selected_preset]
        
        try:
            self.camera_indices = [int(x.strip()) for x in camera_indices_str.split(",")][:self.num_cameras]
            self.num_cameras = len(self.camera_indices)
        except ValueError:
            self.camera_indices = [0, 2, 4, 6]
            self.num_cameras = 4
        
        # Resolution with performance focus
        self.st.sidebar.subheader("Resolution (Lower = Higher FPS)")
        resolution_presets = {
            "320x240 (Maximum FPS)": (320, 240),
            "640x360 (Balanced)": (640, 360),
            "640x480 (Standard)": (640, 480),
            "800x600 (Quality)": (800, 600),
        }
        
        selected_resolution = self.st.sidebar.selectbox("Resolution", list(resolution_presets.keys()))
        self.frame_width, self.frame_height = resolution_presets[selected_resolution]
        
        # Performance tuning
        self.st.sidebar.subheader("🔧 Performance Tuning")
        self.target_fps = self.st.sidebar.slider("Target FPS", 15, 60, 30)
        self.frame_skip = self.st.sidebar.slider("Display Frame Skip", 1, 5, 1)
        self.thread_delay = self.st.sidebar.slider("Thread Delay (ms)", 5, 50, 10) / 1000
        
        # Advanced settings - optimized based on test results
        with self.st.sidebar.expander("🔬 Hardware-Optimized Settings"):
            self.st.sidebar.info("⚠️ Based on test results:\n• Low buffer hurts (-48%)\n• MJPEG helps (+0.4%)\n• High FPS setting hurts (-33%)")
            self.buffer_size = self.st.sidebar.slider("Camera Buffer Size", 3, 10, 5)  # Start from 3
            self.use_threading_optimization = self.st.sidebar.checkbox("Threading Optimization", True)
            self.use_mjpeg_force = self.st.sidebar.checkbox("Force MJPEG Codec", True)
            self.reduce_quality = self.st.sidebar.checkbox("Reduce Display Quality", False)
        
        self.st.sidebar.info(f"Current: {self.frame_width}x{self.frame_height}")
        self.st.sidebar.info(f"Cameras: {self.camera_indices}")
        
        self.create_camera_layout()

    def create_camera_layout(self) -> None:
        """Create optimized camera layout."""
        self.overall_fps_container = self.st.empty()
        
        # Create compact grid
        cols_per_row = 2 if self.num_cameras <= 4 else 3
        rows = (self.num_cameras + cols_per_row - 1) // cols_per_row
        
        for row in range(rows):
            cols = self.st.columns(cols_per_row)
            for col_idx in range(cols_per_row):
                cam_idx = row * cols_per_row + col_idx
                if cam_idx < self.num_cameras:
                    with cols[col_idx]:
                        fps_container = self.st.empty()
                        frame_container = self.st.empty()
                        self.camera_containers[cam_idx] = frame_container
                        self.fps_containers[cam_idx] = fps_container

    def setup_cameras_optimized(self) -> bool:
        """Setup cameras with aggressive optimizations."""
        self.st.info(f"🚀 Initializing {len(self.camera_indices)} cameras with optimizations...")
        
        self.camera_caps = []
        self.camera_frames = {}
        self.camera_fps = {}
        self.camera_frame_times = {}
        self.camera_frame_counts = {}
        self.camera_start_times = {}
        
        successful_cameras = 0
        
        for i, cam_idx in enumerate(self.camera_indices):
            try:
                # Try different backends for better performance
                backends = [cv2.CAP_V4L2, cv2.CAP_ANY]
                cap = None
                
                for backend in backends:
                    try:
                        cap = cv2.VideoCapture(cam_idx, backend)
                        if cap.isOpened():
                            break
                        cap.release()
                    except:
                        continue
                
                if cap is None or not cap.isOpened():
                    cap = cv2.VideoCapture(cam_idx)
                
                if cap.isOpened():
                    # Hardware-specific optimization settings
                    # Based on test results: low buffer hurts performance (-48%)
                    cap.set(cv2.CAP_PROP_BUFFERSIZE, max(3, self.buffer_size))  # Minimum 3

                    if self.use_mjpeg_force:
                        # MJPEG helps slightly (+0.4%)
                        cap.set(cv2.CAP_PROP_FOURCC, cv2.VideoWriter_fourcc(*'MJPG'))

                    # Set resolution - avoid too low resolution as it can hurt performance
                    cap.set(cv2.CAP_PROP_FRAME_WIDTH, self.frame_width)
                    cap.set(cv2.CAP_PROP_FRAME_HEIGHT, self.frame_height)

                    # Don't force high FPS as it can hurt performance (-33%)
                    # Let camera use its natural FPS

                    # Skip auto feature disabling as it hurt performance (-19%)
                    
                    # Test capture with minimal delay
                    time.sleep(0.05)
                    
                    ret, test_frame = cap.read()
                    if ret and test_frame is not None:
                        actual_width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
                        actual_height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
                        actual_fps = cap.get(cv2.CAP_PROP_FPS)
                        
                        self.camera_caps.append(cap)
                        self.camera_frames[i] = test_frame
                        self.camera_fps[i] = 0.0
                        self.camera_frame_times[i] = deque(maxlen=10)  # Smaller window for faster response
                        self.camera_frame_counts[i] = 0
                        self.camera_start_times[i] = time.time()
                        successful_cameras += 1
                        
                        LOGGER.info(f"Camera {cam_idx}: {actual_width}x{actual_height} @ {actual_fps} FPS")
                    else:
                        self.camera_caps.append(None)
                        cap.release()
                else:
                    self.camera_caps.append(None)
                
                time.sleep(0.05)  # Minimal delay
                
            except Exception as e:
                self.camera_caps.append(None)
                LOGGER.error(f"Error with camera {cam_idx}: {e}")
        
        if successful_cameras > 0:
            self.st.success(f"✅ {successful_cameras}/{len(self.camera_indices)} cameras ready!")
            return True
        else:
            self.st.error("❌ No cameras initialized")
            return False

    def optimized_camera_thread(self, camera_index: int, cap: cv2.VideoCapture) -> None:
        """Highly optimized camera capture thread."""
        frame_counter = 0
        last_fps_calc = time.time()
        
        while self.camera_running and cap.isOpened():
            try:
                ret, frame = cap.read()
                if ret and frame is not None:
                    # Store frame
                    self.camera_frames[camera_index] = frame
                    frame_counter += 1
                    self.camera_frame_counts[camera_index] += 1
                    
                    # Fast FPS calculation every 10 frames
                    if frame_counter % 10 == 0:
                        current_time = time.time()
                        elapsed = current_time - last_fps_calc
                        if elapsed > 0:
                            self.camera_fps[camera_index] = 10 / elapsed
                        last_fps_calc = current_time
                    
                    # Moderate delay - not too aggressive as it can hurt performance
                    time.sleep(max(0.02, self.thread_delay))  # Minimum 20ms
                else:
                    time.sleep(0.01)  # Short delay on failure
                    
            except Exception as e:
                LOGGER.error(f"Camera {camera_index} error: {e}")
                time.sleep(0.1)

    def start_optimized_monitoring(self) -> None:
        """Start optimized monitoring."""
        if not self.setup_cameras_optimized():
            return
        
        # Start optimized threads
        self.camera_running = True
        self.camera_threads = []
        
        for i, cap in enumerate(self.camera_caps):
            if cap is not None:
                if self.use_threading_optimization:
                    thread = threading.Thread(
                        target=self.optimized_camera_thread, 
                        args=(i, cap),
                        daemon=True
                    )
                    thread.start()
                    self.camera_threads.append(thread)
        
        stop_button = self.st.sidebar.button("🛑 Stop Monitoring")
        
        try:
            monitoring_start = time.time()
            display_frame_counter = 0
            last_display_update = time.time()
            
            while self.camera_running:
                if stop_button:
                    break
                
                current_time = time.time()
                display_frame_counter += 1
                
                # Update display every N frames or every second
                if (display_frame_counter % self.frame_skip == 0 or 
                    current_time - last_display_update >= 1.0):
                    
                    # Update camera displays
                    for cam_idx in range(len(self.camera_caps)):
                        if cam_idx in self.camera_frames and cam_idx in self.camera_containers:
                            frame = self.camera_frames[cam_idx]
                            
                            if frame is not None:
                                # Reduce quality if enabled
                                if self.reduce_quality:
                                    frame = cv2.resize(frame, 
                                        (self.frame_width//2, self.frame_height//2))
                                
                                # Display frame
                                self.camera_containers[cam_idx].image(
                                    frame, channels="BGR", 
                                    caption=f"Camera {self.camera_indices[cam_idx]}"
                                )
                    
                    # Update FPS displays
                    for cam_idx in range(len(self.camera_caps)):
                        if cam_idx in self.fps_containers and cam_idx in self.camera_fps:
                            fps = self.camera_fps[cam_idx]
                            color = "🟢" if fps > 20 else "🟡" if fps > 10 else "🔴"
                            self.fps_containers[cam_idx].metric(
                                f"{color} Cam {self.camera_indices[cam_idx]}", 
                                f"{fps:.1f} FPS"
                            )
                    
                    # Update overall stats
                    if self.overall_fps_container:
                        elapsed = current_time - monitoring_start
                        total_frames = sum(self.camera_frame_counts.values())
                        overall_fps = total_frames / elapsed if elapsed > 0 else 0
                        avg_fps = sum(self.camera_fps.values()) / len(self.camera_fps) if self.camera_fps else 0
                        
                        self.overall_fps_container.markdown(f"""
                        ### ⚡ Optimized Performance Metrics
                        - **Overall FPS**: {overall_fps:.1f} | **Average Camera FPS**: {avg_fps:.1f}
                        - **Total Frames**: {total_frames:,} | **Runtime**: {elapsed:.1f}s
                        - **Resolution**: {self.frame_width}x{self.frame_height} | **Target**: {self.target_fps} FPS
                        """)
                    
                    last_display_update = current_time
                
                # Minimal sleep for UI responsiveness
                time.sleep(0.01)
        
        finally:
            self.cleanup()

    def cleanup(self) -> None:
        """Clean up resources."""
        self.camera_running = False
        
        for thread in self.camera_threads:
            if thread.is_alive():
                thread.join(timeout=0.5)
        
        for cap in self.camera_caps:
            if cap is not None:
                cap.release()
        
        cv2.destroyAllWindows()

    def run(self) -> None:
        """Run the optimized application."""
        self.web_ui()
        self.sidebar()
        
        if self.st.sidebar.button("🚀 Start Optimized Monitoring"):
            self.start_optimized_monitoring()


def main():
    """Main function."""
    monitor = OptimizedCameraFPSMonitor()
    monitor.run()


if __name__ == "__main__":
    main()
