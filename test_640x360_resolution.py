#!/usr/bin/env python3
"""
Test 640x360 Resolution Support
测试640x360分辨率支持

This script tests if cameras support the new 640x360 resolution option.
"""

import cv2
import time
import sys


def test_resolution_support(camera_indices=[0, 2, 4, 6], target_resolution=(640, 360)):
    """
    Test if cameras support the specified resolution.
    
    Args:
        camera_indices: List of camera indices to test
        target_resolution: Target resolution as (width, height)
    """
    width, height = target_resolution
    print(f"🎯 Testing {width}x{height} resolution support")
    print("=" * 50)
    
    results = {}
    
    for cam_idx in camera_indices:
        print(f"📹 Testing Camera {cam_idx}...")
        
        try:
            cap = cv2.VideoCapture(cam_idx)
            
            if not cap.isOpened():
                results[cam_idx] = {"status": "failed", "reason": "Cannot open camera"}
                print(f"  ❌ Cannot open camera {cam_idx}")
                continue
            
            # Set target resolution
            cap.set(cv2.CAP_PROP_FRAME_WIDTH, width)
            cap.set(cv2.CAP_PROP_FRAME_HEIGHT, height)
            
            # Get actual resolution
            actual_width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            actual_height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            
            # Test frame capture
            ret, frame = cap.read()
            
            if ret and frame is not None:
                frame_height, frame_width = frame.shape[:2]
                
                # Check if resolution matches
                resolution_match = (actual_width == width and actual_height == height)
                frame_match = (frame_width == width and frame_height == height)
                
                results[cam_idx] = {
                    "status": "success" if resolution_match and frame_match else "partial",
                    "requested": f"{width}x{height}",
                    "actual_props": f"{actual_width}x{actual_height}",
                    "actual_frame": f"{frame_width}x{frame_height}",
                    "resolution_match": resolution_match,
                    "frame_match": frame_match
                }
                
                if resolution_match and frame_match:
                    print(f"  ✅ Perfect match: {frame_width}x{frame_height}")
                elif frame_match:
                    print(f"  ✅ Frame match: {frame_width}x{frame_height} (props: {actual_width}x{actual_height})")
                else:
                    print(f"  ⚠️  Partial support: got {frame_width}x{frame_height}, props: {actual_width}x{actual_height}")
                    
            else:
                results[cam_idx] = {"status": "failed", "reason": "Cannot read frames"}
                print(f"  ❌ Cannot read frames from camera {cam_idx}")
            
            cap.release()
            
        except Exception as e:
            results[cam_idx] = {"status": "error", "reason": str(e)}
            print(f"  ❌ Error with camera {cam_idx}: {e}")
        
        time.sleep(0.2)  # Small delay between tests
    
    return results


def print_summary(results, target_resolution):
    """Print test summary."""
    width, height = target_resolution
    
    print(f"\n📋 {width}x{height} Resolution Test Summary")
    print("=" * 50)
    
    success_count = 0
    partial_count = 0
    failed_count = 0
    
    for cam_idx, result in results.items():
        status = result["status"]
        
        if status == "success":
            success_count += 1
            print(f"✅ Camera {cam_idx}: Full support")
        elif status == "partial":
            partial_count += 1
            actual = result.get("actual_frame", "unknown")
            print(f"⚠️  Camera {cam_idx}: Partial support (got {actual})")
        else:
            failed_count += 1
            reason = result.get("reason", "unknown error")
            print(f"❌ Camera {cam_idx}: Failed ({reason})")
    
    total = len(results)
    print(f"\n📊 Results:")
    print(f"  • Total cameras tested: {total}")
    print(f"  • Full support: {success_count}")
    print(f"  • Partial support: {partial_count}")
    print(f"  • Failed: {failed_count}")
    
    if success_count > 0:
        print(f"\n🎉 {success_count} camera(s) fully support {width}x{height} resolution!")
    
    if partial_count > 0:
        print(f"⚠️  {partial_count} camera(s) have partial support (may still work)")
    
    if failed_count == total:
        print(f"❌ No cameras support {width}x{height} resolution")
        print("💡 Try alternative resolutions like 640x480 or 320x240")


def test_multiple_resolutions():
    """Test multiple common resolutions."""
    resolutions = [
        (640, 360),   # 16:9 widescreen
        (640, 480),   # 4:3 standard
        (320, 240),   # QVGA
        (800, 600),   # SVGA
        (1280, 720),  # HD
    ]
    
    camera_indices = [0, 2, 4, 6]
    
    print("🔍 Testing Multiple Resolutions")
    print("=" * 50)
    
    all_results = {}
    
    for resolution in resolutions:
        width, height = resolution
        print(f"\n🎯 Testing {width}x{height}...")
        
        results = test_resolution_support(camera_indices, resolution)
        all_results[f"{width}x{height}"] = results
        
        # Quick summary
        success_count = sum(1 for r in results.values() if r["status"] == "success")
        print(f"  📊 {success_count}/{len(results)} cameras fully support {width}x{height}")
    
    # Final comparison
    print(f"\n📋 Resolution Compatibility Matrix")
    print("=" * 60)
    print(f"{'Camera':<8} {'640x360':<8} {'640x480':<8} {'320x240':<8} {'800x600':<8} {'1280x720':<9}")
    print("-" * 60)
    
    for cam_idx in camera_indices:
        row = f"{cam_idx:<8}"
        for res_name in ["640x360", "640x480", "320x240", "800x600", "1280x720"]:
            if res_name in all_results and cam_idx in all_results[res_name]:
                status = all_results[res_name][cam_idx]["status"]
                symbol = "✅" if status == "success" else "⚠️" if status == "partial" else "❌"
                row += f" {symbol:<7}"
            else:
                row += f" {'❓':<7}"
        print(row)


if __name__ == "__main__":
    print("🎥 Camera Resolution Support Test")
    print("=" * 50)
    
    if len(sys.argv) > 1 and sys.argv[1] == "--all":
        # Test multiple resolutions
        test_multiple_resolutions()
    else:
        # Test only 640x360
        print("Testing 640x360 resolution (use --all for comprehensive test)")
        print("")
        
        results = test_resolution_support([0, 2, 4, 6], (640, 360))
        print_summary(results, (640, 360))
        
        print(f"\n💡 Tips:")
        print(f"  • Use --all flag to test multiple resolutions")
        print(f"  • 640x360 is 16:9 widescreen format")
        print(f"  • Lower resolutions typically have better FPS")
        print(f"  • Some cameras may auto-adjust to nearest supported resolution")
