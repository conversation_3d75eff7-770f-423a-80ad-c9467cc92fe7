#!/usr/bin/env python3
"""
测试MJPG压缩解决方案
基于诊断结果，MJPG可以将带宽从59.3 Mbps降低到14.8 Mbps
"""

import cv2
import time
import threading
from collections import defaultdict

def test_mjpg_bandwidth():
    """测试MJPG格式的带宽优势"""
    print("🎨 MJPG压缩解决方案测试")
    print("=" * 50)
    
    camera_indices = [0, 2, 4, 6]
    
    print("📊 带宽对比 (640x360):")
    print("   YUYV格式: 59.3 Mbps × 4 = 237.2 Mbps")
    print("   MJPG格式: 14.8 Mbps × 4 = 59.2 Mbps")
    print("   节省带宽: 75% !")
    
    # 测试MJPG格式
    print(f"\n🧪 测试MJPG格式4摄像头同时工作...")
    
    cameras = {}
    
    # 初始化所有摄像头为MJPG格式
    for cam_idx in camera_indices:
        try:
            cap = cv2.VideoCapture(cam_idx)
            if cap.isOpened():
                # 强制MJPG格式
                cap.set(cv2.CAP_PROP_FOURCC, cv2.VideoWriter_fourcc(*'MJPG'))
                cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
                cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 360)
                cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)
                
                time.sleep(0.2)  # 等待设置生效
                
                # 验证格式
                actual_fourcc = int(cap.get(cv2.CAP_PROP_FOURCC))
                actual_format = "".join([chr((actual_fourcc >> 8 * i) & 0xFF) for i in range(4)])
                actual_width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
                actual_height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
                
                print(f"   摄像头 {cam_idx}: {actual_format} {actual_width}x{actual_height}")
                
                # 测试读取
                ret, frame = cap.read()
                if ret and frame is not None:
                    cameras[cam_idx] = cap
                    print(f"   ✅ 摄像头 {cam_idx}: MJPG初始化成功")
                else:
                    print(f"   ❌ 摄像头 {cam_idx}: 无法读取帧")
                    cap.release()
            else:
                print(f"   ❌ 摄像头 {cam_idx}: 无法打开")
        except Exception as e:
            print(f"   ❌ 摄像头 {cam_idx}: 异常 {e}")
    
    if len(cameras) >= 3:
        print(f"\n🎉 成功初始化 {len(cameras)} 个MJPG摄像头!")
        
        # 测试同时捕获
        print(f"📊 测试同时捕获5秒...")
        
        frame_counts = defaultdict(int)
        errors = defaultdict(int)
        
        def capture_thread(cam_idx, cap):
            for _ in range(50):  # 5秒 × 10fps
                try:
                    ret, frame = cap.read()
                    if ret and frame is not None:
                        frame_counts[cam_idx] += 1
                    else:
                        errors[cam_idx] += 1
                except Exception as e:
                    errors[cam_idx] += 1
                time.sleep(0.1)
        
        # 启动线程
        threads = []
        for cam_idx, cap in cameras.items():
            thread = threading.Thread(target=capture_thread, args=(cam_idx, cap))
            thread.start()
            threads.append(thread)
        
        # 等待完成
        for thread in threads:
            thread.join()
        
        # 结果分析
        print(f"\n📈 MJPG测试结果:")
        total_success = 0
        total_expected = len(cameras) * 50
        
        for cam_idx in cameras.keys():
            success = frame_counts[cam_idx]
            error = errors[cam_idx]
            success_rate = success / 50
            total_success += success
            
            print(f"   摄像头 {cam_idx}: {success}/50 成功 ({success_rate*100:.1f}%), {error} 错误")
        
        overall_success_rate = total_success / total_expected
        print(f"\n🎯 总体成功率: {overall_success_rate*100:.1f}%")
        
        if overall_success_rate >= 0.8:
            print("🎉 MJPG方案成功! 可以支持4摄像头同时工作!")
        else:
            print("⚠️  MJPG方案部分成功，但仍有稳定性问题")
    
    else:
        print(f"❌ MJPG方案失败，只能初始化 {len(cameras)} 个摄像头")
    
    # 清理
    for cap in cameras.values():
        cap.release()
    
    return len(cameras)

def test_progressive_mjpg():
    """渐进式MJPG测试"""
    print(f"\n🔄 渐进式MJPG测试")
    print("=" * 50)
    
    camera_indices = [0, 2, 4, 6]
    working_cameras = []
    
    for i, cam_idx in enumerate(camera_indices, 1):
        print(f"\n📷 添加第{i}个MJPG摄像头 (索引{cam_idx})...")
        
        cap = cv2.VideoCapture(cam_idx)
        if cap.isOpened():
            # 设置MJPG
            cap.set(cv2.CAP_PROP_FOURCC, cv2.VideoWriter_fourcc(*'MJPG'))
            cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
            cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 360)
            cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)
            
            time.sleep(0.2)
            
            # 测试稳定性
            success_count = 0
            for _ in range(10):
                ret, frame = cap.read()
                if ret and frame is not None:
                    success_count += 1
                time.sleep(0.1)
            
            success_rate = success_count / 10
            print(f"   稳定性: {success_rate*100:.1f}%")
            
            if success_rate >= 0.8:
                working_cameras.append((cam_idx, cap))
                print(f"   ✅ 摄像头{cam_idx}添加成功")
                
                # 测试所有当前摄像头
                if len(working_cameras) >= 2:
                    print(f"   🔄 测试{len(working_cameras)}个MJPG摄像头同时工作...")
                    
                    all_stable = True
                    for test_cam_idx, test_cap in working_cameras:
                        ret, frame = test_cap.read()
                        if not (ret and frame is not None):
                            all_stable = False
                            break
                    
                    if not all_stable:
                        print(f"   ❌ 同时工作不稳定，移除摄像头{cam_idx}")
                        cap.release()
                        working_cameras.pop()
                        break
            else:
                print(f"   ❌ 摄像头{cam_idx}不稳定")
                cap.release()
        else:
            print(f"   ❌ 无法打开摄像头{cam_idx}")
    
    print(f"\n📊 MJPG渐进式测试结果:")
    print(f"   最大同时工作摄像头: {len(working_cameras)}")
    print(f"   工作的摄像头: {[cam_idx for cam_idx, _ in working_cameras]}")
    
    # 清理
    for cam_idx, cap in working_cameras:
        cap.release()
    
    return len(working_cameras)

def generate_mjpg_integration():
    """生成MJPG集成代码"""
    print(f"\n💻 MJPG集成代码")
    print("=" * 50)
    
    print("✅ 已自动更新 ultralytics/solutions/streamlit_inference.py")
    print("   添加了强制MJPG格式设置")
    
    print(f"\n🔧 手动优化选项:")
    print(f"   在sidebar中添加格式选择:")
    
    code_example = '''
# 在sidebar方法中添加
format_options = {
    "MJPG (推荐)": cv2.VideoWriter_fourcc(*'MJPG'),
    "YUYV (默认)": cv2.VideoWriter_fourcc(*'YUYV'),
    "自动": None
}

selected_format = self.st.sidebar.selectbox("视频格式", list(format_options.keys()))
self.video_format = format_options[selected_format]

# 在setup_cameras中使用
if self.video_format:
    cap.set(cv2.CAP_PROP_FOURCC, self.video_format)
'''
    
    print(code_example)

def main():
    print("🎨 MJPG压缩解决方案")
    print("=" * 60)
    
    print("💡 基于诊断结果:")
    print("   - MJPG格式可减少75%带宽使用")
    print("   - 从237.2 Mbps降低到59.2 Mbps")
    print("   - 理论上可支持4摄像头同时工作")
    
    # 测试MJPG方案
    mjpg_cameras = test_mjpg_bandwidth()
    
    # 渐进式测试
    progressive_cameras = test_progressive_mjpg()
    
    # 生成集成代码
    generate_mjpg_integration()
    
    print(f"\n🎯 MJPG方案结论:")
    if mjpg_cameras >= 4 or progressive_cameras >= 4:
        print(f"   🎉 成功! MJPG可以支持4摄像头")
        print(f"   📋 建议: 使用更新后的Streamlit应用")
    elif mjpg_cameras >= 3 or progressive_cameras >= 3:
        print(f"   ✅ 部分成功! MJPG可以支持3摄像头")
        print(f"   📋 建议: 比原来的2摄像头有改进")
    else:
        print(f"   ❌ MJPG方案效果有限")
        print(f"   📋 建议: 考虑USB 3.0方案")

if __name__ == "__main__":
    main()
